@echo off
chcp 65001 >nul
echo ========================================
echo 量化股票软件完整版打包脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo ✓ 环境检查完成

echo.
echo 正在检查必要文件...

:: 检查主要文件是否存在
if not exist "登录注册.py" (
    echo 错误: 未找到登录注册.py
    pause
    exit /b 1
)

if not exist "股票看图软件_增强版.py" (
    echo 错误: 未找到股票看图软件_增强版.py
    pause
    exit /b 1
)

if not exist "市场数据管理.py" (
    echo 错误: 未找到市场数据管理.py
    pause
    exit /b 1
)

echo ✓ 必要文件检查完成

echo.
echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "量化股票软件.exe" del "量化股票软件.exe"

echo.
echo 正在创建必要的目录和文件...

:: 确保tushare_token.txt存在
if not exist "tushare_token.txt" (
    echo 创建默认token文件...
    echo. > tushare_token.txt
)

:: 确保配置文件存在
if not exist "browser_config.json" (
    echo 创建默认浏览器配置...
    echo {} > browser_config.json
)

if not exist "multi_stock_config.json" (
    echo 创建默认多股票配置...
    echo {} > multi_stock_config.json
)

:: 确保目录存在
if not exist "user_config" mkdir "user_config"
if not exist "market_data_cache" mkdir "market_data_cache"
if not exist "策略示例" mkdir "策略示例"

echo ✓ 目录和文件准备完成

echo.
echo 正在开始打包...
echo 这可能需要几分钟时间，请耐心等待...

:: 使用PyInstaller打包
pyinstaller --clean 量化股票软件完整版.spec

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ✓ 打包完成！

:: 检查生成的exe文件
if exist "dist\量化股票软件.exe" (
    echo.
    echo ========================================
    echo 🎉 打包成功！
    echo ========================================
    echo.
    echo 生成的文件位置: dist\量化股票软件.exe
    echo.
    echo 文件大小:
    for %%I in ("dist\量化股票软件.exe") do echo %%~zI 字节
    echo.
    echo 使用说明:
    echo 1. 双击 dist\量化股票软件.exe 启动程序
    echo 2. 首次运行会显示登录注册界面
    echo 3. 登录成功后会自动启动股票看图软件
    echo 4. 所有功能已集成在一个exe文件中
    echo.
    echo 功能特点:
    echo ✓ 保持获取数据每分钟的限制量
    echo ✓ 多股票回测，板块选择功能完整
    echo ✓ 错误处理优化，用户友好提示
    echo ✓ 单文件exe，无需额外依赖
    echo.
    
    :: 询问是否立即测试
    set /p test_choice="是否立即测试运行？(y/n): "
    if /i "%test_choice%"=="y" (
        echo.
        echo 正在启动测试...
        start "" "dist\量化股票软件.exe"
        echo 测试程序已启动，请检查功能是否正常
    )
    
) else (
    echo.
    echo ❌ 打包失败：未找到生成的exe文件
    echo 请检查打包过程中的错误信息
)

echo.
echo 打包脚本执行完成
pause
