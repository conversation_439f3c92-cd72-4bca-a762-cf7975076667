#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare注册自动化UI界面
完全后台运行，用户只需在弹出窗口中操作
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import base64
from PIL import Image, ImageTk
import io
import os
import json
import subprocess
import sys

# 尝试导入不同浏览器的驱动管理器
try:
    from webdriver_manager.microsoft import EdgeChromiumDriverManager
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.edge.options import Options as EdgeOptions
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.chrome.service import Service as ChromeService
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False


class BrowserDriverManager:
    """浏览器驱动管理器 - 负责检测、安装和缓存浏览器驱动"""

    def __init__(self):
        self.config_file = "browser_config.json"
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {
            "chrome_driver_path": None,
            "edge_driver_path": None,
            "last_check_time": 0,
            "preferred_browser": None
        }

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def check_system_chrome_driver(self):
        """检查系统是否已安装Chrome驱动"""
        try:
            service = ChromeService()
            # 尝试创建Chrome驱动实例来测试
            options = ChromeOptions()
            options.add_argument('--headless=new')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')

            driver = webdriver.Chrome(service=service, options=options)
            driver.quit()
            return True
        except Exception:
            return False

    def check_system_edge_driver(self):
        """检查系统是否已安装Edge驱动"""
        try:
            service = EdgeService()
            # 尝试创建Edge驱动实例来测试
            options = EdgeOptions()
            options.add_argument('--headless=new')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')

            driver = webdriver.Edge(service=service, options=options)
            driver.quit()
            return True
        except Exception:
            return False

    def install_chrome_driver(self):
        """安装Chrome驱动"""
        if not WEBDRIVER_MANAGER_AVAILABLE:
            print("webdriver-manager 不可用，无法自动安装Chrome驱动")
            return None

        try:
            print("正在下载并安装Chrome驱动...")
            driver_path = ChromeDriverManager().install()
            self.config["chrome_driver_path"] = driver_path
            self.config["last_check_time"] = time.time()
            self.save_config()
            print(f"Chrome驱动安装成功: {driver_path}")
            return driver_path
        except Exception as e:
            print(f"Chrome驱动安装失败: {e}")
            return None

    def install_edge_driver(self):
        """安装Edge驱动"""
        if not WEBDRIVER_MANAGER_AVAILABLE:
            print("webdriver-manager 不可用，无法自动安装Edge驱动")
            return None

        try:
            print("正在下载并安装Edge驱动...")
            driver_path = EdgeChromiumDriverManager().install()
            self.config["edge_driver_path"] = driver_path
            self.config["last_check_time"] = time.time()
            self.save_config()
            print(f"Edge驱动安装成功: {driver_path}")
            return driver_path
        except Exception as e:
            print(f"Edge驱动安装失败: {e}")
            return None

    def get_best_browser_setup(self):
        """获取最佳的浏览器设置"""
        # 检查是否需要重新检测（每天检测一次）
        current_time = time.time()
        if current_time - self.config.get("last_check_time", 0) > 86400:  # 24小时
            print("开始检测浏览器驱动状态...")

            # 检测系统Chrome驱动
            if self.check_system_chrome_driver():
                print("检测到系统Chrome驱动可用")
                self.config["preferred_browser"] = "chrome_system"
                self.config["last_check_time"] = current_time
                self.save_config()
                return "chrome_system", None

            # 检测系统Edge驱动
            if self.check_system_edge_driver():
                print("检测到系统Edge驱动可用")
                self.config["preferred_browser"] = "edge_system"
                self.config["last_check_time"] = current_time
                self.save_config()
                return "edge_system", None

            # 系统驱动都不可用，尝试安装
            print("系统驱动不可用，尝试安装驱动...")

            # 优先安装Chrome驱动
            chrome_path = self.install_chrome_driver()
            if chrome_path:
                self.config["preferred_browser"] = "chrome_installed"
                return "chrome_installed", chrome_path

            # Chrome安装失败，尝试Edge
            edge_path = self.install_edge_driver()
            if edge_path:
                self.config["preferred_browser"] = "edge_installed"
                return "edge_installed", edge_path

            print("所有浏览器驱动安装都失败")
            return None, None

        # 使用缓存的配置
        preferred = self.config.get("preferred_browser")
        if preferred == "chrome_system":
            return "chrome_system", None
        elif preferred == "edge_system":
            return "edge_system", None
        elif preferred == "chrome_installed":
            return "chrome_installed", self.config.get("chrome_driver_path")
        elif preferred == "edge_installed":
            return "edge_installed", self.config.get("edge_driver_path")

        # 没有缓存配置，进行检测
        return self.get_best_browser_setup()


class TushareUI:
    def __init__(self):
        print("开始初始化 TushareUI...")

        # 初始化变量（在创建UI之前）
        self.driver = None
        self.captcha_image = None
        self.current_mode = "login"  # 当前模式：login 或 register
        self.current_panel = "login"  # 当前显示的面板：login 或 register
        self.driver_initialized = False  # 标记浏览器是否已初始化
        self.browser_type = None  # 记录使用的浏览器类型
        self.browser_driver_path = None  # 记录驱动路径

        # 验证码验证相关变量
        self.captcha_validation_timer = None
        self.last_validation_time = 0
        self.captcha_sync_timer = None
        self.is_syncing = False
        self.captcha_error_state = False  # 标记是否处于错误状态

        # 初始化浏览器驱动管理器
        self.browser_manager = BrowserDriverManager()

        print("变量初始化完成")

        # 智能检测和准备浏览器驱动
        print("正在检测浏览器驱动...")
        browser_type, driver_path = self.browser_manager.get_best_browser_setup()

        if browser_type:
            self.browser_type = browser_type
            self.browser_driver_path = driver_path
            print(f"浏览器驱动准备完成: {browser_type}")

            # 快速初始化浏览器（现在应该很快）
            print("正在初始化浏览器...")
            try:
                if self.init_driver_fast():
                    self.driver_initialized = True
                    print("浏览器初始化成功")
                else:
                    print("浏览器初始化失败，将在需要时重新尝试")
            except Exception as e:
                print(f"浏览器初始化异常: {str(e)}")
        else:
            print("浏览器驱动准备失败，将在需要时尝试初始化")

        print("开始创建UI界面...")
        # 创建UI界面
        self.root = tk.Tk()
        self.root.title("量化股票软件Tus")
        self.root.geometry("650x600")
        self.root.resizable(False, False)

        # 设置窗口居中
        self.center_window()

        # 创建UI界面
        self.create_widgets()

        # 默认显示登录界面
        self.show_login_panel()

        print("TushareUI 初始化完成")
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """创建UI组件"""
        # 主标题
        self.title_label = tk.Label(self.root, text="量化股票软件Tus",
                                   font=("Arial", 16, "bold"), fg="blue")
        self.title_label.pack(pady=10)

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建可滚动的容器
        self.canvas = tk.Canvas(self.main_frame)
        self.scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 创建登录和注册面板的容器
        self.login_frame = ttk.Frame(self.scrollable_frame)
        self.register_frame = ttk.Frame(self.scrollable_frame)

        # 创建登录面板
        self.create_login_panel()

        # 创建注册面板
        self.create_register_panel()
        
        # 创建隐藏的状态文本用于内部日志
        self.status_text = None

    def create_login_panel(self):
        """创建登录面板"""
        # 副标题
        subtitle_label = tk.Label(self.login_frame, text="用户登录",
                                 font=("Arial", 14, "bold"), fg="darkblue")
        subtitle_label.pack(pady=10)

        # 手机号输入
        phone_frame = ttk.Frame(self.login_frame)
        phone_frame.pack(fill=tk.X, pady=5)
        ttk.Label(phone_frame, text="手机号:", width=12).pack(side=tk.LEFT)
        self.login_phone_var = tk.StringVar()
        self.login_phone_entry = ttk.Entry(phone_frame, textvariable=self.login_phone_var, width=20)
        self.login_phone_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 密码自动设置为123456（不显示输入框）
        self.login_password_var = tk.StringVar(value="123456")

        # 验证码图片显示区域
        self.login_captcha_frame = ttk.Frame(self.login_frame)
        self.login_captcha_frame.pack(pady=5)
        self.login_captcha_label = ttk.Label(self.login_captcha_frame, text="正在自动获取验证码...")
        self.login_captcha_label.pack()

        # 图片验证码输入
        login_captcha_input_frame = ttk.Frame(self.login_frame)
        login_captcha_input_frame.pack(fill=tk.X, pady=5)
        ttk.Label(login_captcha_input_frame, text="图片验证码:", width=12).pack(side=tk.LEFT)
        self.login_captcha_var = tk.StringVar()
        self.login_captcha_entry = ttk.Entry(login_captcha_input_frame, textvariable=self.login_captcha_var, width=15)
        self.login_captcha_entry.pack(side=tk.LEFT, padx=(5, 5))

        # 验证码状态指示器
        self.login_captcha_status_label = tk.Label(login_captcha_input_frame, text="", width=25, font=("Arial", 9))
        self.login_captcha_status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 刷新验证码按钮
        self.login_refresh_captcha_btn = ttk.Button(self.login_frame, text="刷新验证码",
                                                   command=self.refresh_login_captcha, state=tk.DISABLED)
        self.login_refresh_captcha_btn.pack(pady=5)

        # 登录按钮
        self.login_btn = ttk.Button(self.login_frame, text="登录",
                                   command=self.submit_login, state=tk.DISABLED)
        self.login_btn.pack(pady=10)

        # 切换到注册按钮
        switch_frame = ttk.Frame(self.login_frame)
        switch_frame.pack(pady=5)
        ttk.Label(switch_frame, text="没有账号？").pack(side=tk.LEFT)
        switch_register_btn = ttk.Button(switch_frame, text="注册", command=self.show_register_panel)
        switch_register_btn.pack(side=tk.LEFT, padx=(5, 0))

        # 登录状态提示标签
        self.login_status_label = tk.Label(self.login_frame, text="",
                                          font=("Arial", 11, "bold"),
                                          fg="green", wraplength=400)
        self.login_status_label.pack(pady=10)

        # 绑定登录验证码输入事件
        self.login_captcha_var.trace('w', self.on_login_captcha_change)

    def create_register_panel(self):
        """创建注册面板"""
        # 副标题
        subtitle_label = tk.Label(self.register_frame, text="用户注册",
                                 font=("Arial", 14, "bold"), fg="darkgreen")
        subtitle_label.pack(pady=10)

        # 手机号输入
        phone_frame = ttk.Frame(self.register_frame)
        phone_frame.pack(fill=tk.X, pady=5)
        ttk.Label(phone_frame, text="手机号:", width=12).pack(side=tk.LEFT)
        self.phone_var = tk.StringVar()
        self.phone_entry = ttk.Entry(phone_frame, textvariable=self.phone_var, width=20)
        self.phone_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 密码自动设置为123456（不显示输入框）
        self.password_var = tk.StringVar(value="123456")

        # 验证码图片显示区域
        self.captcha_frame = ttk.Frame(self.register_frame)
        self.captcha_frame.pack(pady=5)
        self.captcha_label = ttk.Label(self.captcha_frame, text="正在自动获取验证码...")
        self.captcha_label.pack()

        # 图片验证码输入
        captcha_input_frame = ttk.Frame(self.register_frame)
        captcha_input_frame.pack(fill=tk.X, pady=5)
        ttk.Label(captcha_input_frame, text="图片验证码:", width=12).pack(side=tk.LEFT)
        self.captcha_var = tk.StringVar()
        self.captcha_entry = ttk.Entry(captcha_input_frame, textvariable=self.captcha_var, width=15)
        self.captcha_entry.pack(side=tk.LEFT, padx=(5, 5))

        # 验证码状态指示器
        self.captcha_status_label = tk.Label(captcha_input_frame, text="", width=25, font=("Arial", 9))
        self.captcha_status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 刷新验证码按钮
        self.refresh_captcha_btn = ttk.Button(self.register_frame, text="刷新验证码",
                                             command=self.refresh_captcha, state=tk.DISABLED)
        self.refresh_captcha_btn.pack(pady=5)

        # 发送手机验证码按钮
        self.send_sms_btn = ttk.Button(self.register_frame, text="发送手机验证码",
                                      command=self.send_sms_code, state=tk.DISABLED)
        self.send_sms_btn.pack(pady=5)

        # 手机验证码输入
        sms_frame = ttk.Frame(self.register_frame)
        sms_frame.pack(fill=tk.X, pady=5)
        ttk.Label(sms_frame, text="手机验证码:", width=12).pack(side=tk.LEFT)
        self.sms_var = tk.StringVar()
        self.sms_entry = ttk.Entry(sms_frame, textvariable=self.sms_var, width=20)
        self.sms_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 注册按钮
        self.register_btn = ttk.Button(self.register_frame, text="提交注册",
                                      command=self.submit_register, state=tk.DISABLED)
        self.register_btn.pack(pady=10)

        # 切换到登录按钮
        switch_frame = ttk.Frame(self.register_frame)
        switch_frame.pack(pady=5)
        ttk.Label(switch_frame, text="已有账号？").pack(side=tk.LEFT)
        switch_login_btn = ttk.Button(switch_frame, text="登录", command=self.show_login_panel)
        switch_login_btn.pack(side=tk.LEFT, padx=(5, 0))

        # 注册状态提示标签
        self.register_status_label = tk.Label(self.register_frame, text="",
                                            font=("Arial", 11, "bold"),
                                            fg="green", wraplength=400)
        self.register_status_label.pack(pady=10)

        # 绑定注册验证码输入事件
        self.captcha_var.trace('w', self.on_captcha_change)

    def log_status(self, message):
        """在状态框中显示消息（现在隐藏，只在控制台输出）"""
        if hasattr(self, 'status_text') and self.status_text:
            self.status_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
            self.status_text.see(tk.END)
        # 在控制台输出日志（用于调试）
        print(f"{time.strftime('%H:%M:%S')} - {message}")
        # 安全地更新UI（只在主线程且UI运行时）
        try:
            if hasattr(self, 'root') and self.root and threading.current_thread() is threading.main_thread():
                self.root.update()
        except Exception:
            # 忽略UI更新错误，不影响核心功能
            pass

    def show_success_message(self, message):
        """在UI界面显示成功提示信息"""
        try:
            # 根据当前显示的面板来选择对应的状态标签
            if hasattr(self, 'current_panel') and self.current_panel == 'register':
                # 在注册面板显示
                if hasattr(self, 'register_status_label'):
                    self.register_status_label.config(text=f"✓ {message}", fg="green")
            else:
                # 在登录面板显示
                if hasattr(self, 'login_status_label'):
                    self.login_status_label.config(text=f"✓ {message}", fg="green")

            # 记录日志
            self.log_status(f"显示成功提示: {message}")

        except Exception as e:
            # 如果显示失败，至少在日志中记录
            self.log_status(f"显示成功提示失败: {str(e)}")

    def clear_status_message(self):
        """清除状态提示信息"""
        try:
            if hasattr(self, 'login_status_label'):
                self.login_status_label.config(text="")
            if hasattr(self, 'register_status_label'):
                self.register_status_label.config(text="")
        except Exception as e:
            self.log_status(f"清除状态提示失败: {str(e)}")

    def show_error_message(self, message):
        """在UI界面显示错误提示信息"""
        try:
            # 根据当前显示的面板来选择对应的状态标签
            if hasattr(self, 'current_panel') and self.current_panel == 'register':
                # 在注册面板显示
                if hasattr(self, 'register_status_label'):
                    self.register_status_label.config(text=f"✗ {message}", fg="red")
            else:
                # 在登录面板显示
                if hasattr(self, 'login_status_label'):
                    self.login_status_label.config(text=f"✗ {message}", fg="red")

            # 记录日志
            self.log_status(f"显示错误提示: {message}")

        except Exception as e:
            self.log_status(f"显示错误提示失败: {str(e)}")



    def auto_get_captcha(self):
        """程序启动时自动获取验证码"""
        def run_auto_get():
            # 短暂延迟让界面完全加载，减少到0.5秒
            time.sleep(0.5)
            self.get_captcha()

        # 在新线程中运行
        threading.Thread(target=run_auto_get, daemon=True).start()

    def show_login_panel(self):
        """显示登录面板"""
        self.current_mode = "login"
        self.current_panel = "login"  # 添加面板跟踪
        self.title_label.config(text="量化股票软件Tus - 登录")
        self.register_frame.pack_forget()
        self.login_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        self.clear_status_message()  # 切换面板时清除状态信息

        # 显示滚动条和画布
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 调整窗口大小适应登录面板
        self.root.geometry("650x450")

        # 停止注册模式的同步
        self.is_syncing = False
        if self.captcha_sync_timer:
            self.root.after_cancel(self.captcha_sync_timer)

        # 启动登录模式的验证码获取
        self.auto_get_login_captcha()

    def show_register_panel(self):
        """显示注册面板"""
        self.current_mode = "register"
        self.current_panel = "register"  # 添加面板跟踪
        self.title_label.config(text="量化股票软件Tus - 注册")
        self.login_frame.pack_forget()
        self.register_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        self.clear_status_message()  # 切换面板时清除状态信息

        # 显示滚动条和画布
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 调整窗口大小适应注册面板
        self.root.geometry("650x600")

        # 停止登录模式的同步
        self.is_syncing = False
        if self.captcha_sync_timer:
            self.root.after_cancel(self.captcha_sync_timer)

        # 启动注册模式的验证码获取
        self.auto_get_captcha()

    def auto_get_login_captcha(self):
        """登录模式自动获取验证码"""
        def run_auto_get():
            time.sleep(0.5)
            self.get_login_captcha()

        threading.Thread(target=run_auto_get, daemon=True).start()

    def on_login_captcha_change(self, *args):
        """登录验证码输入变化时的回调函数"""
        current_captcha = self.login_captcha_var.get().strip()

        if len(current_captcha) == 4:
            self.update_login_captcha_status("已输入", "green")
        elif len(current_captcha) > 0:
            self.update_login_captcha_status("输入中", "blue")
        else:
            self.update_login_captcha_status("等待中", "orange")

    def update_login_captcha_status(self, status, color):
        """更新登录验证码状态显示"""
        self.login_captcha_status_label.config(text=status, fg=color)

        # 根据状态启用/禁用登录按钮
        if status == "已输入" and len(self.login_captcha_var.get().strip()) == 4:
            self.login_btn.config(state=tk.NORMAL)
        elif status in ["等待中", "输入中"]:
            self.login_btn.config(state=tk.DISABLED)

    def on_captcha_change(self, *args):
        """验证码输入变化时的回调函数"""
        # 如果处于错误状态，用户开始输入新验证码时清除错误状态
        current_captcha = self.captcha_var.get().strip()

        if self.captcha_error_state and len(current_captcha) > 0:
            # 用户开始输入新验证码，清除错误状态
            self.captcha_error_state = False

        # 只有在非错误状态下才更新状态显示
        if not self.captcha_error_state:
            if len(current_captcha) == 4:
                self.update_captcha_status("已输入", "green")
            elif len(current_captcha) > 0:
                self.update_captcha_status("输入中", "blue")
            else:
                self.update_captcha_status("等待中", "orange")



    def update_captcha_status(self, status, color):
        """更新验证码状态显示"""
        self.captcha_status_label.config(text=status, fg=color)

        # 根据状态启用/禁用发送验证码按钮
        if status in ["已输入", "验证通过"] and len(self.captcha_var.get().strip()) == 4:
            self.send_sms_btn.config(state=tk.NORMAL)
        elif status in ["等待中", "输入中", "验证中", "验证失败", "验证出错", "验证码输入错误请重新输入验证码"]:
            self.send_sms_btn.config(state=tk.DISABLED)
        # 其他状态保持当前状态

    def start_captcha_sync(self):
        """开始实时同步验证码输入"""
        self.is_syncing = True
        self.sync_captcha_input()

    def sync_captcha_input(self):
        """同步验证码输入到网页"""
        def run_sync():
            try:
                if not self.driver or not self.is_syncing:
                    return

                # 获取当前UI中的验证码输入
                current_captcha = self.captcha_var.get().strip()

                # 获取网页中验证码输入框的当前值
                captcha_element = self.driver.find_element(By.ID, "register-captcha")
                web_captcha = captcha_element.get_attribute("value") or ""

                # 如果不一致，同步到网页
                if current_captcha != web_captcha:
                    captcha_element.clear()
                    if current_captcha:
                        captcha_element.send_keys(current_captcha)

                # 只有在非错误状态下才根据输入长度更新状态
                if not self.captcha_error_state:
                    if len(current_captcha) == 4:
                        # 输入完整，显示就绪状态
                        self.root.after(0, lambda: self.update_captcha_status("已同步", "green"))
                    elif len(current_captcha) > 0:
                        # 正在输入，显示输入状态
                        self.root.after(0, lambda: self.update_captcha_status("同步中", "blue"))
                    else:
                        # 长度不足，显示等待状态
                        self.root.after(0, lambda: self.update_captcha_status("等待中", "orange"))

            except Exception as e:
                # 同步过程出错
                self.root.after(0, lambda: self.update_captcha_status("未知", "gray"))

        # 在新线程中运行同步
        threading.Thread(target=run_sync, daemon=True).start()

        # 每200ms同步一次
        if self.is_syncing:
            self.captcha_sync_timer = self.root.after(200, self.sync_captcha_input)

    def check_captcha_sync_status(self):
        """检查验证码同步状态（简化版）"""
        try:
            # 获取当前UI中的验证码输入
            current_captcha = self.captcha_var.get().strip()

            # 获取网页中验证码输入框的当前值
            captcha_element = self.driver.find_element(By.ID, "register-captcha")
            web_captcha = captcha_element.get_attribute("value") or ""

            # 检查是否同步成功
            if current_captcha == web_captcha:
                if len(current_captcha) == 4:
                    self.root.after(0, lambda: self.update_captcha_status("已同步", "green"))
                elif len(current_captcha) > 0:
                    self.root.after(0, lambda: self.update_captcha_status("同步中", "blue"))
                else:
                    self.root.after(0, lambda: self.update_captcha_status("等待中", "orange"))
            else:
                self.root.after(0, lambda: self.update_captcha_status("同步中", "blue"))

        except Exception as e:
            self.root.after(0, lambda: self.update_captcha_status("未知", "gray"))

    def smart_wait_for_captcha(self):
        """智能等待验证码图片，一出现就立即返回"""
        max_wait_time = 5  # 减少到5秒
        check_interval = 0.1  # 减少到100ms检查一次，更快响应
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # 检查验证码图片元素是否存在
                captcha_img = self.driver.find_element(By.ID, "register-captcha-img")
                if captcha_img and captcha_img.is_displayed():
                    # 检查图片是否有src属性且不为空
                    src = captcha_img.get_attribute("src")
                    if src and src != "":
                        # 减少等待时间，图片一有src就返回
                        time.sleep(0.1)
                        return captcha_img
            except:
                pass

            # 短暂等待后再次检查
            time.sleep(check_interval)

        # 超时返回None
        return None

    def init_driver_fast(self):
        """快速初始化浏览器驱动（使用预检测的配置）"""
        # 清理之前的驱动实例
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

        if not self.browser_type:
            return self.init_driver()  # 回退到原始方法

        try:
            if self.browser_type.startswith("chrome"):
                return self.try_init_chrome_fast()
            elif self.browser_type.startswith("edge"):
                return self.try_init_edge_fast()
        except Exception as e:
            print(f"快速初始化失败: {e}")
            return self.init_driver()  # 回退到原始方法

        return False

    def init_driver(self):
        """智能初始化浏览器驱动（自动选择Edge或Chrome）"""
        # 清理之前的驱动实例
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

        # 首先尝试Chrome浏览器（系统驱动优先）
        if self.try_init_chrome():
            return True

        # Chrome失败，尝试Edge浏览器
        if self.try_init_edge():
            return True

        # 两个都失败
        self.log_status("所有浏览器初始化都失败")
        return False

    def try_init_chrome_fast(self):
        """快速初始化Chrome浏览器（使用预检测的配置）"""
        try:
            self.log_status("正在快速初始化Chrome浏览器...")

            chrome_options = ChromeOptions()
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--no-first-run')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--mute-audio')
            chrome_options.add_argument('--window-position=-2000,-2000')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            chrome_options.page_load_strategy = 'eager'

            if self.browser_type == "chrome_system":
                # 使用系统驱动
                service = ChromeService()
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # 使用已安装的驱动
                service = ChromeService(self.browser_driver_path)
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

            self.browser_type = "chrome"
            self.log_status("Chrome浏览器快速初始化成功")
            # 测试连接
            self.driver.get("about:blank")
            return True

        except Exception as e:
            self.log_status(f"Chrome浏览器快速初始化失败: {str(e)}")
            return False

    def try_init_edge_fast(self):
        """快速初始化Edge浏览器（使用预检测的配置）"""
        try:
            self.log_status("正在快速初始化Edge浏览器...")

            edge_options = EdgeOptions()
            edge_options.add_argument('--headless=new')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--window-size=1920,1080')
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_argument('--disable-extensions')
            edge_options.add_argument('--no-first-run')
            edge_options.add_argument('--disable-default-apps')
            edge_options.add_argument('--disable-popup-blocking')
            edge_options.add_argument('--disable-notifications')
            edge_options.add_argument('--mute-audio')
            edge_options.add_argument('--disable-background-networking')
            edge_options.add_argument('--disable-sync')
            edge_options.add_argument('--disable-translate')
            edge_options.add_argument('--window-position=-2000,-2000')
            edge_options.add_argument('--disable-background-timer-throttling')
            edge_options.add_argument('--disable-backgrounding-occluded-windows')
            edge_options.add_argument('--disable-renderer-backgrounding')
            edge_options.add_argument('--disable-features=TranslateUI')
            edge_options.add_argument('--disable-ipc-flooding-protection')
            edge_options.page_load_strategy = 'eager'

            if self.browser_type == "edge_system":
                # 使用系统驱动
                service = EdgeService()
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Edge(service=service, options=edge_options)
            else:
                # 使用已安装的驱动
                service = EdgeService(self.browser_driver_path)
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Edge(service=service, options=edge_options)

            self.browser_type = "edge"
            self.log_status("Edge浏览器快速初始化成功")
            # 测试连接
            self.driver.get("about:blank")
            return True

        except Exception as e:
            self.log_status(f"Edge浏览器快速初始化失败: {str(e)}")
            return False

    def try_init_edge(self):
        """尝试初始化Edge浏览器（优先使用系统驱动）"""
        try:
            self.log_status("正在尝试初始化Edge浏览器...")

            edge_options = EdgeOptions()
            edge_options.add_argument('--headless=new')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--window-size=1920,1080')
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_argument('--disable-extensions')
            edge_options.add_argument('--no-first-run')
            edge_options.add_argument('--disable-default-apps')
            edge_options.add_argument('--disable-popup-blocking')
            edge_options.add_argument('--disable-notifications')
            edge_options.add_argument('--mute-audio')
            edge_options.add_argument('--disable-background-networking')
            edge_options.add_argument('--disable-sync')
            edge_options.add_argument('--disable-translate')
            edge_options.add_argument('--window-position=-2000,-2000')
            # 添加更多稳定性选项
            edge_options.add_argument('--disable-background-timer-throttling')
            edge_options.add_argument('--disable-backgrounding-occluded-windows')
            edge_options.add_argument('--disable-renderer-backgrounding')
            edge_options.add_argument('--disable-features=TranslateUI')
            edge_options.add_argument('--disable-ipc-flooding-protection')
            edge_options.page_load_strategy = 'eager'

            # 优先尝试系统Edge驱动（更稳定）
            try:
                service = EdgeService()
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Edge(service=service, options=edge_options)
                self.browser_type = "edge"
                self.log_status("Edge浏览器初始化成功（使用系统驱动）")
                # 测试连接
                self.driver.get("about:blank")
                return True
            except Exception as e:
                self.log_status(f"系统Edge驱动失败: {str(e)}")

            # 系统驱动失败，尝试webdriver-manager（如果可用）
            if WEBDRIVER_MANAGER_AVAILABLE:
                try:
                    self.log_status("尝试使用webdriver-manager下载Edge驱动...")
                    service = EdgeService(EdgeChromiumDriverManager().install())
                    service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                    self.driver = webdriver.Edge(service=service, options=edge_options)
                    self.browser_type = "edge"
                    self.log_status("Edge浏览器初始化成功（使用webdriver-manager）")
                    # 测试连接
                    self.driver.get("about:blank")
                    return True
                except Exception as e:
                    self.log_status(f"Edge webdriver-manager失败: {str(e)}")

            return False

        except Exception as e:
            self.log_status(f"Edge浏览器初始化失败: {str(e)}")
            return False

    def try_init_chrome(self):
        """尝试初始化Chrome浏览器（优先使用系统驱动）"""
        try:
            self.log_status("正在尝试初始化Chrome浏览器...")

            chrome_options = ChromeOptions()
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--no-first-run')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--mute-audio')
            chrome_options.add_argument('--window-position=-2000,-2000')
            # 添加更多稳定性选项
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            chrome_options.page_load_strategy = 'eager'

            # 优先尝试系统Chrome驱动（更稳定）
            try:
                service = ChromeService()
                service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.browser_type = "chrome"
                self.log_status("Chrome浏览器初始化成功（使用系统驱动）")
                # 测试连接
                self.driver.get("about:blank")
                return True
            except Exception as e:
                self.log_status(f"系统Chrome驱动失败: {str(e)}")

            # 系统驱动失败，尝试webdriver-manager（如果可用）
            if WEBDRIVER_MANAGER_AVAILABLE:
                try:
                    self.log_status("尝试使用webdriver-manager下载Chrome驱动...")
                    service = ChromeService(ChromeDriverManager().install())
                    service.creation_flags = 0x08000000  # CREATE_NO_WINDOW
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.browser_type = "chrome"
                    self.log_status("Chrome浏览器初始化成功（使用webdriver-manager）")
                    # 测试连接
                    self.driver.get("about:blank")
                    return True
                except Exception as e:
                    self.log_status(f"Chrome webdriver-manager失败: {str(e)}")

            return False

        except Exception as e:
            self.log_status(f"Chrome浏览器初始化失败: {str(e)}")
            return False

    def is_driver_alive(self):
        """检查浏览器驱动是否仍然活跃"""
        try:
            if not self.driver:
                return False
            # 尝试获取当前URL来测试连接
            self.driver.current_url
            return True
        except Exception as e:
            self.log_status(f"浏览器连接检查失败: {str(e)}")
            return False

    def ensure_driver_ready(self):
        """确保浏览器驱动准备就绪"""
        if not self.driver_initialized or not self.is_driver_alive():
            self.log_status("浏览器连接丢失，正在重新初始化...")
            # 优先使用快速初始化
            success = False
            if hasattr(self, 'browser_type') and self.browser_type:
                success = self.init_driver_fast()

            if not success:
                success = self.init_driver()

            if not success:
                self.log_status("浏览器重新初始化失败")
                return False
            self.driver_initialized = True
            self.log_status("浏览器重新初始化完成")
        return True

    def retry_browser_operation(self, operation, max_retries=3):
        """重试浏览器操作，处理连接丢失"""
        for attempt in range(max_retries):
            try:
                if not self.ensure_driver_ready():
                    continue
                return operation()
            except Exception as e:
                self.log_status(f"浏览器操作失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    self.driver_initialized = False  # 标记需要重新初始化
                    time.sleep(1)  # 短暂等待后重试
                else:
                    raise e
        return None

    def get_captcha(self):
        """获取图片验证码"""
        def run_get_captcha():
            try:
                # 确保浏览器驱动准备就绪
                if not self.ensure_driver_ready():
                    return

                self.log_status("正在访问注册页面...")
                self.driver.get("https://tushare.pro/register?reg=677360")

                # 智能等待验证码图片元素，一出现就立即处理
                self.log_status("等待验证码图片加载...")
                captcha_img = self.smart_wait_for_captcha()

                if not captcha_img:
                    self.log_status("验证码图片加载超时")
                    return

                self.log_status("验证码图片已加载，正在获取...")
                
                # 获取验证码图片
                captcha_src = captcha_img.get_attribute("src")

                # 如果是base64图片
                if captcha_src.startswith("data:image"):
                    img_data = captcha_src.split(",")[1]
                    img_bytes = base64.b64decode(img_data)
                else:
                    # 如果是URL，需要获取图片
                    # 构建完整的URL
                    if captcha_src.startswith("/"):
                        captcha_src = "https://tushare.pro" + captcha_src

                    # 获取当前页面的cookies
                    cookies = {}
                    for cookie in self.driver.get_cookies():
                        cookies[cookie['name']] = cookie['value']

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'https://tushare.pro/register?reg=677360'
                    }

                    img_response = requests.get(captcha_src, cookies=cookies, headers=headers)
                    img_bytes = img_response.content

                # 显示验证码图片
                self.display_captcha_image(img_bytes)
                self.log_status("验证码图片获取成功，请输入验证码")

                # 启用刷新按钮，发送验证码按钮等待验证码验证通过
                self.refresh_captcha_btn.config(state=tk.NORMAL)
                # 初始化验证码状态
                self.update_captcha_status("等待中", "orange")

                # 开始实时同步验证码输入
                self.start_captcha_sync()

            except Exception as e:
                self.log_status(f"获取验证码失败: {str(e)}")
                messagebox.showerror("错误", f"获取验证码失败: {str(e)}")

        # 在新线程中运行
        threading.Thread(target=run_get_captcha, daemon=True).start()

    def refresh_captcha(self):
        """刷新验证码图片"""
        def run_refresh():
            try:
                if not self.driver:
                    self.log_status("请先获取验证码")
                    return

                self.log_status("正在刷新验证码...")

                # 点击验证码图片来刷新
                captcha_img = self.driver.find_element(By.ID, "register-captcha-img")
                captcha_img.click()

                # 等待新验证码加载
                time.sleep(1)

                # 重新获取验证码图片
                captcha_src = captcha_img.get_attribute("src")

                # 处理图片数据
                if captcha_src.startswith("data:image"):
                    img_data = captcha_src.split(",")[1]
                    img_bytes = base64.b64decode(img_data)
                else:
                    if captcha_src.startswith("/"):
                        captcha_src = "https://tushare.pro" + captcha_src

                    cookies = {}
                    for cookie in self.driver.get_cookies():
                        cookies[cookie['name']] = cookie['value']

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'https://tushare.pro/register?reg=677360'
                    }

                    img_response = requests.get(captcha_src, cookies=cookies, headers=headers)
                    img_bytes = img_response.content

                # 显示新的验证码图片
                self.display_captcha_image(img_bytes)
                self.log_status("验证码已刷新")

                # 清空验证码输入框并设置错误状态
                self.captcha_var.set("")
                self.captcha_error_state = True  # 设置错误状态标志
                self.update_captcha_status("验证码输入错误请重新输入验证码", "red")

                # 重新开始同步
                self.start_captcha_sync()

            except Exception as e:
                self.log_status(f"刷新验证码失败: {str(e)}")
                messagebox.showerror("错误", f"刷新验证码失败: {str(e)}")

        # 在新线程中运行
        threading.Thread(target=run_refresh, daemon=True).start()
        
    def display_captcha_image(self, img_bytes):
        """显示验证码图片"""
        try:
            # 将字节数据转换为PIL图片
            img = Image.open(io.BytesIO(img_bytes))
            
            # 调整图片大小
            img = img.resize((120, 40), Image.Resampling.LANCZOS)
            
            # 转换为tkinter可用的格式
            self.captcha_image = ImageTk.PhotoImage(img)
            
            # 更新标签显示图片
            self.captcha_label.config(image=self.captcha_image, text="")
            
        except Exception as e:
            self.log_status(f"显示验证码图片失败: {str(e)}")
            
    def send_sms_code(self):
        """发送手机验证码（先验证图片验证码）"""
        def run_send_sms():
            try:
                phone = self.phone_var.get().strip()
                password = self.password_var.get().strip()  # 自动使用123456
                captcha = self.captcha_var.get().strip()

                if not phone:
                    messagebox.showerror("错误", "请填写手机号")
                    return

                if not captcha:
                    messagebox.showerror("错误", "请输入图片验证码")
                    return

                self.log_status("正在验证图片验证码...")
                self.update_captcha_status("验证中", "orange")

                # 先验证图片验证码
                captcha_valid = self.validate_captcha_before_send(phone, password, captcha)

                if not captcha_valid:
                    # 验证码错误，不继续发送
                    return

                self.log_status("图片验证码正确，正在发送手机验证码...")
                self.update_captcha_status("验证通过", "green")

                # 点击发送验证码按钮
                send_btn = self.driver.find_element(By.ID, "register-send_code")
                send_btn.click()

                # 等待发送结果和可能的错误提示
                time.sleep(3)

                # 检查是否有"手机已注册"的错误提示
                phone_registered_error = self.check_phone_registered_error()

                if phone_registered_error:
                    # 手机号已注册，提示用户使用登录功能
                    self.log_status("检测到手机号已注册")
                    result = messagebox.askyesno(
                        "手机号已注册",
                        f"该手机号已注册！\n\n是否切换到登录界面使用该手机号登录？\n\n点击'是'切换到登录界面\n点击'否'继续当前操作"
                    )
                    if result:
                        # 用户选择切换到登录界面
                        self.log_status("用户选择切换到登录界面")
                        # 将手机号复制到登录界面
                        self.login_phone_var.set(phone)
                        # 切换到登录面板
                        self.root.after(500, self.show_login_panel)
                    return

                # 检查其他可能的错误提示
                other_error = self.check_other_sms_errors()
                if other_error:
                    messagebox.showerror("发送失败", f"发送手机验证码失败：\n{other_error}")
                    return

                self.log_status("手机验证码发送请求已提交，请查看手机短信")

                # 启用注册按钮
                self.register_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log_status(f"发送手机验证码失败: {str(e)}")
                messagebox.showerror("错误", f"发送手机验证码失败: {str(e)}")

        # 在新线程中运行
        threading.Thread(target=run_send_sms, daemon=True).start()

    def validate_captcha_before_send(self, phone, password, captcha):
        """在发送验证码前验证图片验证码"""
        try:
            # 填写所有信息
            phone_input = self.driver.find_element(By.ID, "register-account")
            phone_input.clear()
            phone_input.send_keys(phone)

            password_input = self.driver.find_element(By.ID, "register-password")
            password_input.clear()
            password_input.send_keys(password)

            captcha_input = self.driver.find_element(By.ID, "register-captcha")
            captcha_input.clear()
            captcha_input.send_keys(captcha)

            # 点击其他元素触发验证码验证（模拟点击手机号输入框）
            phone_input.click()

            # 等待验证结果
            time.sleep(1)

            # 检查是否有验证码错误提示
            error_found = self.check_captcha_error()

            if error_found:
                self.log_status("图片验证码错误，正在刷新验证码...")
                self.captcha_error_state = True  # 设置错误状态标志
                self.update_captcha_status("验证失败", "red")

                # 自动刷新验证码
                self.root.after(1000, self.refresh_captcha)  # 1秒后刷新
                return False
            else:
                self.log_status("图片验证码验证通过")
                return True

        except Exception as e:
            self.log_status(f"验证码验证过程出错: {str(e)}")
            self.update_captcha_status("验证出错", "gray")
            return False

    def check_captcha_error(self):
        """检查网页中是否有验证码错误提示"""
        try:
            # 检查验证码错误信息元素
            error_selectors = [
                "#register-captcha-info",
                ".error",
                ".alert-danger",
                ".text-danger",
                ".text-red"
            ]

            for selector in error_selectors:
                try:
                    if selector.startswith("#"):
                        element = self.driver.find_element(By.ID, selector[1:])
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element.is_displayed():
                        error_text = element.text.strip()
                        if error_text and ("验证码" in error_text or "错误" in error_text):
                            self.log_status(f"发现错误提示: {error_text}")
                            return True
                except:
                    continue

            # 检查是否有红色文字提示
            try:
                red_elements = self.driver.find_elements(By.CSS_SELECTOR, "[style*='color: red'], [style*='color:red'], .text-red")
                for element in red_elements:
                    if element.is_displayed() and "验证码" in element.text:
                        self.log_status(f"发现红色错误提示: {element.text}")
                        return True
            except:
                pass

            return False

        except Exception as e:
            self.log_status(f"检查错误提示时出错: {str(e)}")
            return False

    def check_phone_registered_error(self):
        """检查是否有手机号已注册的错误提示"""
        try:
            # 等待错误提示显示
            time.sleep(2)

            self.log_status("开始检查手机号注册状态...")

            # 首先检查Tushare特定的错误信息元素
            tushare_error_selectors = [
                "register-common-info",    # 通用错误信息
                "register-account-info",   # 账号相关错误信息
                "register-captcha-info"    # 验证码错误信息
            ]

            for element_id in tushare_error_selectors:
                try:
                    element = self.driver.find_element(By.ID, element_id)
                    # 检查元素是否可见（没有hidden类或display:none）
                    is_visible = element.is_displayed()
                    has_hidden_class = "hidden" in element.get_attribute("class")

                    self.log_status(f"检查元素 {element_id}: 可见={is_visible}, 有hidden类={has_hidden_class}")

                    if is_visible and not has_hidden_class:
                        # 检查元素内的label文本
                        try:
                            label = element.find_element(By.TAG_NAME, "label")
                            error_text = label.text.strip()
                            self.log_status(f"元素 {element_id} 的错误文本: '{error_text}'")

                            if error_text and any(keyword in error_text for keyword in
                                ["已注册", "已存在", "已被使用", "已被注册", "重复", "exist", "registered", "已经注册"]):
                                self.log_status(f"发现手机已注册提示: {error_text}")
                                return error_text
                        except:
                            # 如果没有label，检查元素本身的文本
                            error_text = element.text.strip()
                            self.log_status(f"元素 {element_id} 的直接文本: '{error_text}'")

                            if error_text and any(keyword in error_text for keyword in
                                ["已注册", "已存在", "已被使用", "已被注册", "重复", "exist", "registered", "已经注册"]):
                                self.log_status(f"发现手机已注册提示: {error_text}")
                                return error_text
                except Exception as e:
                    self.log_status(f"检查元素 {element_id} 时出错: {str(e)}")
                    continue

            # 检查页面上所有可能的错误提示
            try:
                all_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '已注册') or contains(text(), '已存在') or contains(text(), '已被使用') or contains(text(), '已经注册')]")
                for element in all_elements:
                    if element.is_displayed():
                        error_text = element.text.strip()
                        self.log_status(f"通过XPath找到错误提示: {error_text}")
                        return error_text
            except Exception as e:
                self.log_status(f"XPath搜索时出错: {str(e)}")

            # 检查alert弹窗
            try:
                alert = self.driver.switch_to.alert
                alert_text = alert.text
                self.log_status(f"发现alert弹窗: {alert_text}")
                if any(keyword in alert_text for keyword in ["已注册", "已存在", "已被使用", "已经注册"]):
                    alert.accept()  # 关闭alert
                    return alert_text
                alert.accept()  # 关闭alert
            except:
                pass  # 没有alert

            self.log_status("未发现手机已注册的错误提示")
            return None

        except Exception as e:
            self.log_status(f"检查手机注册错误时出错: {str(e)}")
            return None

    def check_other_sms_errors(self):
        """检查其他短信发送错误"""
        try:
            self.log_status("检查其他短信发送错误...")

            # 检查Tushare特定的错误信息元素
            tushare_error_ids = ["register-common-info", "register-send_code-info"]

            for element_id in tushare_error_ids:
                try:
                    element = self.driver.find_element(By.ID, element_id)
                    if element.is_displayed() and "hidden" not in element.get_attribute("class"):
                        try:
                            label = element.find_element(By.TAG_NAME, "label")
                            error_text = label.text.strip()
                        except:
                            error_text = element.text.strip()

                        if error_text:
                            # 排除已注册相关的错误（这些由上面的方法处理）
                            if not any(keyword in error_text for keyword in
                                ["已注册", "已存在", "已被使用", "已被注册", "已经注册"]):
                                self.log_status(f"发现其他错误提示: {error_text}")
                                return error_text
                except:
                    continue

            return None

        except Exception as e:
            self.log_status(f"检查其他错误时出错: {str(e)}")
            return None

    def get_login_captcha(self):
        """获取登录页面的验证码"""
        def run_get_login_captcha():
            try:
                # 确保浏览器驱动准备就绪
                if not self.ensure_driver_ready():
                    return

                self.log_status("正在访问登录页面...")
                self.driver.get("https://tushare.pro/login")

                # 智能等待验证码图片元素
                self.log_status("等待验证码图片加载...")
                captcha_img = self.smart_wait_for_login_captcha()

                if not captcha_img:
                    self.log_status("登录验证码图片加载超时")
                    return

                self.log_status("登录验证码图片已加载，正在获取...")

                # 获取验证码图片
                captcha_src = captcha_img.get_attribute("src")

                # 处理图片数据
                if captcha_src.startswith("data:image"):
                    img_data = captcha_src.split(",")[1]
                    img_bytes = base64.b64decode(img_data)
                else:
                    if captcha_src.startswith("/"):
                        captcha_src = "https://tushare.pro" + captcha_src

                    cookies = {}
                    for cookie in self.driver.get_cookies():
                        cookies[cookie['name']] = cookie['value']

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'https://tushare.pro/login'
                    }

                    img_response = requests.get(captcha_src, cookies=cookies, headers=headers)
                    img_bytes = img_response.content

                # 显示验证码图片
                self.display_login_captcha_image(img_bytes)
                self.log_status("登录验证码图片获取成功，请输入验证码")

                # 启用刷新按钮
                self.login_refresh_captcha_btn.config(state=tk.NORMAL)
                # 初始化验证码状态
                self.update_login_captcha_status("等待中", "orange")

            except Exception as e:
                self.log_status(f"获取登录验证码失败: {str(e)}")
                messagebox.showerror("错误", f"获取登录验证码失败: {str(e)}")

        # 在新线程中运行
        threading.Thread(target=run_get_login_captcha, daemon=True).start()

    def smart_wait_for_login_captcha(self):
        """智能等待登录验证码图片"""
        max_wait_time = 5  # 减少到5秒
        check_interval = 0.1  # 减少到100ms检查一次
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                captcha_img = self.driver.find_element(By.ID, "login-captcha-img")
                if captcha_img and captcha_img.is_displayed():
                    # 检查图片是否有src属性且不为空
                    src = captcha_img.get_attribute("src")
                    if src and src != "":
                        time.sleep(0.1)  # 减少等待时间
                        return captcha_img
            except:
                pass

            time.sleep(check_interval)

        return None

    def display_login_captcha_image(self, img_bytes):
        """显示登录验证码图片"""
        try:
            img = Image.open(io.BytesIO(img_bytes))
            img = img.resize((120, 40), Image.Resampling.LANCZOS)
            self.login_captcha_image = ImageTk.PhotoImage(img)
            self.login_captcha_label.config(image=self.login_captcha_image, text="")
        except Exception as e:
            self.log_status(f"显示登录验证码图片失败: {str(e)}")

    def submit_register(self):
        """提交注册"""
        def run_submit():
            try:
                sms_code = self.sms_var.get().strip()
                
                if not sms_code:
                    messagebox.showerror("错误", "请输入手机验证码")
                    return
                
                self.log_status("正在提交注册信息...")
                
                # 填写手机验证码
                sms_input = self.driver.find_element(By.ID, "register-verify_code")
                sms_input.clear()
                sms_input.send_keys(sms_code)
                
                # 点击注册按钮
                register_btn = self.driver.find_element(By.ID, "register-btn")
                register_btn.click()
                
                # 等待结果
                time.sleep(3)
                
                # 检查是否注册成功
                current_url = self.driver.current_url
                self.log_status(f"注册后页面URL: {current_url}")

                # 检查多种注册成功的标志
                registration_success = False

                # 方法1: 检查URL跳转
                if "login" in current_url or "success" in current_url or "user" in current_url:
                    registration_success = True
                    self.log_status("检测到URL跳转，注册成功")

                # 方法2: 检查页面元素（用户菜单或成功提示）
                if not registration_success:
                    try:
                        # 检查是否有用户菜单（说明已登录）
                        user_menu = self.driver.find_element(By.ID, "login-user-btn")
                        if user_menu.is_displayed():
                            registration_success = True
                            self.log_status("检测到用户菜单，注册并自动登录成功")
                    except:
                        pass

                # 方法3: 检查是否离开注册页面
                if not registration_success and "register" not in current_url:
                    registration_success = True
                    self.log_status("已离开注册页面，判断为注册成功")

                if registration_success:
                    self.log_status("注册成功！正在自动填写用户信息...")
                    messagebox.showinfo("成功", "注册成功！正在自动填写用户信息...")
                    # 自动填写用户信息
                    self.auto_fill_user_info()
                else:
                    self.log_status("注册可能失败，请检查输入信息")
                    
            except Exception as e:
                self.log_status(f"提交注册失败: {str(e)}")
                messagebox.showerror("错误", f"提交注册失败: {str(e)}")
            finally:
                if self.driver:
                    self.driver.quit()
                    self.log_status("浏览器已关闭")
        
        # 在新线程中运行
        threading.Thread(target=run_submit, daemon=True).start()
        
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """关闭应用程序时的清理工作"""
        self.graceful_close()

    def refresh_login_captcha(self):
        """刷新登录验证码"""
        def run_refresh():
            try:
                if not self.driver:
                    self.log_status("请先获取验证码")
                    return

                self.log_status("正在刷新登录验证码...")

                # 检查当前页面是否是登录页面
                current_url = self.driver.current_url
                if "login" not in current_url:
                    self.log_status("当前不在登录页面，重新访问登录页面...")
                    self.driver.get("https://tushare.pro/login")
                    time.sleep(2)

                # 智能等待验证码图片元素
                captcha_img = self.smart_wait_for_login_captcha()
                if not captcha_img:
                    self.log_status("无法找到登录验证码图片，重新获取...")
                    # 重新获取整个登录验证码
                    self.get_login_captcha()
                    return

                # 点击刷新验证码
                captcha_img.click()
                time.sleep(1)

                # 重新等待新的验证码图片
                captcha_img = self.smart_wait_for_login_captcha()
                if not captcha_img:
                    self.log_status("刷新后无法获取验证码图片")
                    return

                captcha_src = captcha_img.get_attribute("src")

                if captcha_src.startswith("data:image"):
                    img_data = captcha_src.split(",")[1]
                    img_bytes = base64.b64decode(img_data)
                else:
                    if captcha_src.startswith("/"):
                        captcha_src = "https://tushare.pro" + captcha_src

                    cookies = {}
                    for cookie in self.driver.get_cookies():
                        cookies[cookie['name']] = cookie['value']

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'https://tushare.pro/login'
                    }

                    img_response = requests.get(captcha_src, cookies=cookies, headers=headers)
                    img_bytes = img_response.content

                self.display_login_captcha_image(img_bytes)
                self.log_status("登录验证码已刷新")

                self.login_captcha_var.set("")
                self.update_login_captcha_status("等待中", "orange")

            except Exception as e:
                self.log_status(f"刷新登录验证码失败: {str(e)}")
                # 如果刷新失败，尝试重新获取整个验证码
                self.log_status("尝试重新获取登录验证码...")
                self.get_login_captcha()

        threading.Thread(target=run_refresh, daemon=True).start()

    def submit_login(self):
        """提交登录"""
        def run_login():
            try:
                phone = self.login_phone_var.get().strip()
                password = self.login_password_var.get().strip()
                captcha = self.login_captcha_var.get().strip()

                if not phone:
                    messagebox.showerror("错误", "请填写手机号")
                    return

                if not captcha:
                    messagebox.showerror("错误", "请输入图片验证码")
                    return

                self.log_status("正在登录...")

                # 填写登录信息
                phone_input = self.driver.find_element(By.ID, "login-account")
                phone_input.clear()
                phone_input.send_keys(phone)

                password_input = self.driver.find_element(By.ID, "login-password")
                password_input.clear()
                password_input.send_keys(password)

                self.log_status(f"使用自动密码: {password}")

                captcha_input = self.driver.find_element(By.ID, "login-captcha")
                captcha_input.clear()
                captcha_input.send_keys(captcha)

                # 点击登录按钮
                login_btn = self.driver.find_element(By.ID, "login-btn")
                login_btn.click()

                # 等待登录结果
                time.sleep(3)

                # 检查是否登录成功
                current_url = self.driver.current_url
                self.log_status(f"登录后页面URL: {current_url}")

                # 检查多种登录成功的标志
                login_success = False

                # 方法1: 检查URL跳转
                if "user" in current_url or "token" in current_url or current_url == "https://tushare.pro/":
                    login_success = True
                    self.log_status("检测到URL跳转，登录成功")

                # 方法2: 检查页面元素（用户菜单）
                if not login_success:
                    try:
                        user_menu = self.driver.find_element(By.ID, "login-user-btn")
                        if user_menu.is_displayed():
                            login_success = True
                            self.log_status("检测到用户菜单，登录成功")
                    except:
                        pass

                # 方法3: 检查是否还在登录页面
                if not login_success and "login" not in current_url:
                    login_success = True
                    self.log_status("已离开登录页面，判断为登录成功")

                if login_success:
                    self.log_status("登录成功！正在获取Token...")
                    self.get_token()
                else:
                    # 检查是否有错误提示
                    login_failed = False
                    try:
                        # 检查各种可能的错误提示元素
                        error_selectors = [
                            "#login-common-info",
                            ".error",
                            ".alert-danger",
                            ".text-danger",
                            ".text-red"
                        ]

                        for selector in error_selectors:
                            try:
                                if selector.startswith("#"):
                                    elements = self.driver.find_elements(By.ID, selector[1:])
                                else:
                                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                                for element in elements:
                                    if element.is_displayed() and element.text.strip():
                                        error_text = element.text.strip()
                                        if "验证码" in error_text or "错误" in error_text or "失败" in error_text:
                                            self.log_status(f"登录失败: {error_text}")
                                            if "验证码" in error_text:
                                                # 验证码错误，显示提示并自动刷新验证码
                                                self.log_status("验证码错误，正在自动刷新...")
                                                messagebox.showwarning("验证码错误", "图片验证码输入错误，请重新输入")
                                                self.root.after(1000, self.refresh_login_captcha)
                                                self.update_login_captcha_status("验证码错误，已自动刷新", "red")
                                            else:
                                                messagebox.showerror("登录失败", error_text)
                                            login_failed = True
                                            break
                            except:
                                continue

                        if not login_failed:
                            # 如果没有找到明确的错误信息，检查是否还在登录页面
                            if "login" in current_url:
                                self.log_status("登录可能失败，请检查输入信息")
                                messagebox.showwarning("提示", "登录可能失败，请检查输入信息")
                            else:
                                self.log_status("页面跳转异常，请重试")

                    except Exception as e:
                        self.log_status(f"检查登录结果时出错: {str(e)}")
                        messagebox.showwarning("提示", "登录结果检查异常，请重试")

            except Exception as e:
                self.log_status(f"登录失败: {str(e)}")
                messagebox.showerror("错误", f"登录失败: {str(e)}")

        threading.Thread(target=run_login, daemon=True).start()

    def get_token(self):
        """获取用户Token"""
        def run_get_token():
            try:
                self.log_status("正在访问Token页面...")

                # 访问token页面
                self.driver.get("https://tushare.pro/user/token")
                time.sleep(3)

                # 检查是否成功访问token页面
                current_url = self.driver.current_url
                self.log_status(f"Token页面URL: {current_url}")

                if "token" not in current_url:
                    self.log_status("无法访问Token页面，可能需要重新登录")
                    messagebox.showerror("错误", "无法访问Token页面，请重新登录")
                    return

                # 等待页面完全加载
                self.log_status("等待Token页面加载...")
                time.sleep(2)

                # 尝试多种方式查找token值
                token = None

                # 方法1: 查找hidden input的value
                try:
                    token_element = self.driver.find_element(By.ID, "token-value")
                    token = token_element.get_attribute("value")
                    if token:
                        self.log_status("通过token-value元素获取到Token")
                except:
                    pass

                # 方法2: 查找显示的token input
                if not token:
                    try:
                        token_input = self.driver.find_element(By.ID, "token")
                        token = token_input.get_attribute("value")
                        if token:
                            self.log_status("通过token元素获取到Token")
                    except:
                        pass

                # 方法3: 查找页面中的token文本
                if not token:
                    try:
                        # 查找包含token的文本元素
                        elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'token') or contains(@value, 'token')]")
                        for element in elements:
                            text = element.text or element.get_attribute("value") or ""
                            if len(text) > 30 and len(text) < 100:  # token通常是32-64位
                                token = text
                                self.log_status("通过文本搜索获取到Token")
                                break
                    except:
                        pass

                if token and len(token) > 10:
                    self.log_status(f"Token获取成功: {token}")
                    print(f"\n=== Tushare Token ===")
                    print(f"Token: {token}")
                    print(f"==================\n")

                    # 登录成功，显示成功提示并启动股票看图软件
                    self.log_status("登录成功！正在启动股票看图软件...")

                    # 在UI界面显示成功提示
                    self.show_success_message("登录成功！正在启动股票看图软件...")

                    # 启动股票看图软件并传递Token
                    self.launch_stock_viewer(token)
                else:
                    self.log_status("Token获取失败：可能是验证码错误")

                    # Token获取失败，提示验证码错误并自动刷新验证码
                    messagebox.showwarning("验证码错误", "验证码输入错误请重新输入")
                    self.root.after(1000, self.refresh_login_captcha)
                    self.update_login_captcha_status("验证码错误，已自动刷新", "red")

            except Exception as e:
                self.log_status(f"Token获取失败: {str(e)}")
                messagebox.showerror("错误", f"Token获取失败: {str(e)}")

        threading.Thread(target=run_get_token, daemon=True).start()

    def auto_fill_user_info(self):
        """注册成功后自动填写用户信息"""
        def run_fill_info():
            try:
                self.log_status("正在访问用户信息页面...")

                # 访问用户信息页面
                self.driver.get("https://tushare.pro/user/info")
                time.sleep(3)

                # 检查是否成功访问用户信息页面
                current_url = self.driver.current_url
                self.log_status(f"用户信息页面URL: {current_url}")

                if "info" not in current_url:
                    self.log_status("无法访问用户信息页面")
                    messagebox.showerror("错误", "无法访问用户信息页面")
                    return

                # 等待页面完全加载
                self.log_status("等待用户信息页面加载...")
                time.sleep(2)

                # 生成随机中文姓名
                random_name = self.generate_random_chinese_name()
                self.log_status(f"生成随机姓名: {random_name}")

                # 查找姓名输入框并填入随机姓名
                name_input = None
                name_selectors = [
                    "input[name='name']",
                    "input[name='real_name']",
                    "input[name='username']",
                    "#name",
                    "#real_name",
                    "#username"
                ]

                for selector in name_selectors:
                    try:
                        if selector.startswith("#"):
                            name_input = self.driver.find_element(By.ID, selector[1:])
                        else:
                            name_input = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if name_input and name_input.is_displayed():
                            self.log_status(f"找到姓名输入框: {selector}")
                            break
                    except:
                        continue

                if not name_input:
                    # 尝试通过placeholder或label文本查找
                    try:
                        inputs = self.driver.find_elements(By.TAG_NAME, "input")
                        for input_elem in inputs:
                            placeholder = input_elem.get_attribute("placeholder") or ""
                            if "姓名" in placeholder or "名字" in placeholder or "真实姓名" in placeholder:
                                name_input = input_elem
                                self.log_status("通过placeholder找到姓名输入框")
                                break
                    except:
                        pass

                if name_input:
                    # 清空并填入随机姓名
                    name_input.clear()
                    name_input.send_keys(random_name)
                    self.log_status(f"已填入姓名: {random_name}")

                    # 查找并点击提交按钮
                    submit_success = self.find_and_click_submit_button()

                    if submit_success:
                        self.log_status("用户信息提交成功！正在获取Token...")
                        # 用户信息提交成功后，自动获取Token
                        self.get_token_after_register(random_name)
                    else:
                        self.log_status("未找到提交按钮，请手动提交")
                        messagebox.showinfo("提示", f"姓名已自动填写为: {random_name}\n请手动点击提交按钮\n提交后程序将自动获取Token")
                else:
                    self.log_status("未找到姓名输入框")
                    messagebox.showerror("错误", "未找到姓名输入框，请手动填写")

            except Exception as e:
                self.log_status(f"自动填写用户信息失败: {str(e)}")
                messagebox.showerror("错误", f"自动填写用户信息失败: {str(e)}")

        threading.Thread(target=run_fill_info, daemon=True).start()

    def generate_random_chinese_name(self):
        """生成随机的三个中文字姓名"""
        import random

        # 常用姓氏
        surnames = [
            "王", "李", "张", "刘", "陈", "杨", "黄", "赵", "周", "吴",
            "徐", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗",
            "郑", "梁", "谢", "宋", "唐", "许", "邓", "冯", "韩", "曹"
        ]

        # 常用名字字符
        name_chars = [
            "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
            "勇", "艳", "杰", "娟", "涛", "明", "超", "秀", "霞", "平",
            "刚", "桂", "英", "华", "文", "琴", "玉", "萍", "红", "娇",
            "辉", "鹏", "建", "国", "民", "学", "志", "海", "峰", "东",
            "雪", "梅", "春", "燕", "云", "龙", "飞", "鑫", "宇", "航"
        ]

        # 随机选择一个姓氏和两个名字字符
        surname = random.choice(surnames)
        name1 = random.choice(name_chars)
        name2 = random.choice(name_chars)

        return surname + name1 + name2

    def find_and_click_submit_button(self):
        """查找并点击提交按钮"""
        try:
            # 可能的提交按钮选择器
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('提交')",
                "button:contains('保存')",
                "button:contains('确定')",
                "#submit",
                "#save",
                ".btn-submit",
                ".submit-btn"
            ]

            for selector in submit_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath查找包含特定文本的按钮
                        text = selector.split(":contains('")[1].split("')")[0]
                        buttons = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')]")
                        if buttons:
                            button = buttons[0]
                        else:
                            continue
                    elif selector.startswith("#"):
                        button = self.driver.find_element(By.ID, selector[1:])
                    elif selector.startswith("."):
                        button = self.driver.find_element(By.CLASS_NAME, selector[1:])
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button and button.is_displayed() and button.is_enabled():
                        self.log_status(f"找到提交按钮: {selector}")
                        button.click()
                        time.sleep(2)  # 等待提交完成
                        return True
                except:
                    continue

            # 如果没找到，尝试查找所有按钮并检查文本
            try:
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    button_text = button.text.strip()
                    if any(keyword in button_text for keyword in ["提交", "保存", "确定", "更新", "修改"]):
                        if button.is_displayed() and button.is_enabled():
                            self.log_status(f"通过文本找到提交按钮: {button_text}")
                            button.click()
                            time.sleep(2)
                            return True
            except:
                pass

            return False

        except Exception as e:
            self.log_status(f"查找提交按钮时出错: {str(e)}")
            return False

    def get_token_after_register(self, user_name):
        """注册并填写用户信息后获取Token"""
        def run_get_token():
            try:
                self.log_status("等待用户信息提交完成...")
                time.sleep(3)  # 等待提交完成

                self.log_status("正在访问Token页面...")

                # 访问token页面
                self.driver.get("https://tushare.pro/user/token")
                time.sleep(3)

                # 检查是否成功访问token页面
                current_url = self.driver.current_url
                self.log_status(f"Token页面URL: {current_url}")

                if "token" not in current_url:
                    self.log_status("无法访问Token页面，可能需要重新登录")
                    messagebox.showerror("错误", "无法访问Token页面，请手动访问")
                    return

                # 等待页面完全加载
                self.log_status("等待Token页面加载...")
                time.sleep(2)

                # 尝试多种方式查找token值
                token = None

                # 方法1: 查找hidden input的value
                try:
                    token_element = self.driver.find_element(By.ID, "token-value")
                    token = token_element.get_attribute("value")
                    if token:
                        self.log_status("通过token-value元素获取到Token")
                except:
                    pass

                # 方法2: 查找显示的token input
                if not token:
                    try:
                        token_input = self.driver.find_element(By.ID, "token")
                        token = token_input.get_attribute("value")
                        if token:
                            self.log_status("通过token元素获取到Token")
                    except:
                        pass

                # 方法3: 查找页面中的token文本
                if not token:
                    try:
                        # 查找包含token的文本元素
                        elements = self.driver.find_elements(By.XPATH, "//*[contains(@value, 'token') or contains(text(), 'token')]")
                        for element in elements:
                            text = element.text or element.get_attribute("value") or ""
                            if len(text) > 30 and len(text) < 100:  # token通常是32-64位
                                token = text
                                self.log_status("通过文本搜索获取到Token")
                                break
                    except:
                        pass

                if token and len(token) > 10:
                    self.log_status(f"注册完成！Token获取成功: {token}")
                    print(f"\n=== 注册成功 - Tushare Token ===")
                    print(f"用户姓名: {user_name}")
                    print(f"Token: {token}")
                    print(f"===============================\n")

                    # 记录成功信息到日志
                    self.log_status("🎉 注册流程全部完成！")
                    self.log_status("✅ 账号注册成功")
                    self.log_status(f"✅ 用户信息已填写（姓名: {user_name}）")
                    self.log_status("✅ Token已获取")
                    self.log_status("正在启动股票看图软件...")

                    # 在UI界面显示注册成功提示
                    self.show_success_message("注册成功！正在启动股票看图软件...")

                    # 启动股票看图软件并传递Token（不显示消息框）
                    self.launch_stock_viewer(token)
                else:
                    self.log_status("Token获取失败：未找到有效的token值")

                    # 即使Token获取失败，也显示注册成功的信息
                    success_message = f"✅ 注册流程基本完成！\n\n"
                    success_message += f"✅ 账号注册成功\n"
                    success_message += f"✅ 用户信息已填写（姓名: {user_name}）\n"
                    success_message += f"❌ Token获取失败\n\n"
                    success_message += f"请手动访问 https://tushare.pro/user/token 获取Token"

                    messagebox.showinfo("注册完成", success_message)

            except Exception as e:
                self.log_status(f"注册后Token获取失败: {str(e)}")

                # 即使出错，也显示注册成功的基本信息
                success_message = f"✅ 注册流程基本完成！\n\n"
                success_message += f"✅ 账号注册成功\n"
                success_message += f"✅ 用户信息已填写（姓名: {user_name}）\n"
                success_message += f"❌ Token获取出错: {str(e)}\n\n"
                success_message += f"请手动访问 https://tushare.pro/user/token 获取Token"

                messagebox.showinfo("注册完成", success_message)

        threading.Thread(target=run_get_token, daemon=True).start()

    def launch_stock_viewer(self, token):
        """启动股票看图软件并传递Token"""
        try:
            import subprocess
            import sys
            import os

            # 获取当前目录下的股票看图软件路径
            stock_viewer_path = os.path.join(os.path.dirname(__file__), "股票看图软件_增强版.py")

            if not os.path.exists(stock_viewer_path):
                messagebox.showerror("错误", "未找到股票看图软件文件：股票看图软件_增强版.py")
                return

            # 将Token写入临时文件
            token_file = os.path.join(os.path.dirname(__file__), "tushare_token.txt")
            with open(token_file, 'w', encoding='utf-8') as f:
                f.write(token)

            # 保存用户手机号后四位到监控系统
            try:
                from 使用者监控 import set_user_phone
                # 获取当前登录的手机号
                current_phone = ""
                if hasattr(self, 'current_panel') and self.current_panel == 'login':
                    current_phone = self.login_phone_var.get().strip()
                elif hasattr(self, 'current_panel') and self.current_panel == 'register':
                    current_phone = self.phone_var.get().strip()

                if current_phone:
                    set_user_phone(current_phone)
                    self.log_status(f"已保存用户信息到监控系统")
            except Exception as e:
                self.log_status(f"保存用户信息到监控系统失败: {e}")

            # 创建进程标识文件，用于股票软件关闭时清理登录程序
            pid_file = os.path.join(os.path.dirname(__file__), "login_process.pid")
            with open(pid_file, 'w', encoding='utf-8') as f:
                f.write(str(os.getpid()))

            self.log_status("启动股票看图软件...")

            # 启动股票看图软件，添加错误处理
            try:
                # 在Windows系统中，隐藏控制台窗口启动
                import platform
                if platform.system() == "Windows":
                    # Windows系统：隐藏控制台窗口启动
                    # CREATE_NO_WINDOW = 0x08000000 (隐藏控制台窗口)
                    process = subprocess.Popen([sys.executable, stock_viewer_path],
                                             cwd=os.path.dirname(__file__),
                                             creationflags=0x08000000)  # CREATE_NO_WINDOW
                else:
                    # 其他系统：正常启动
                    process = subprocess.Popen([sys.executable, stock_viewer_path],
                                             cwd=os.path.dirname(__file__))

                # 保存子进程引用
                self.stock_viewer_process = process

                self.log_status("股票看图软件已启动")

                # 等待一小段时间确保程序正常启动
                time.sleep(2)

                # 检查进程是否还在运行
                if process.poll() is None:
                    # 进程正在运行，启动成功
                    self.log_status("股票看图软件运行正常")

                    # 启动监控线程，监控股票软件进程状态
                    self.start_process_monitor(process)

                    # 延迟隐藏登录界面到后台，确保股票软件完全启动
                    def hide_login_after_delay():
                        try:
                            # 再次检查股票软件是否还在运行
                            if process.poll() is None:
                                self.log_status("股票看图软件启动成功，登录界面将隐藏到后台")
                                # 隐藏登录界面到后台，而不是完全关闭
                                self.hide_to_background()
                            else:
                                messagebox.showerror("启动失败", "股票看图软件意外退出")
                                self.graceful_close()
                        except Exception as e:
                            self.log_status(f"检查股票软件状态时出错: {str(e)}")
                            # 即使出错也隐藏到后台
                            self.hide_to_background()

                    # 延迟3秒后隐藏到后台，确保股票软件完全启动
                    self.root.after(3000, hide_login_after_delay)

                else:
                    # 进程已退出，可能启动失败
                    self.log_status("股票看图软件启动失败")
                    messagebox.showerror("启动失败", "股票看图软件启动后立即退出\n请检查程序是否有错误")

            except Exception as e:
                self.log_status(f"启动股票看图软件时出错: {str(e)}")
                messagebox.showerror("启动失败", f"启动股票看图软件时出错:\n{str(e)}")

        except Exception as e:
            self.log_status(f"启动股票看图软件失败: {str(e)}")
            messagebox.showerror("启动失败", f"启动股票看图软件失败:\n{str(e)}\n\n请手动启动股票看图软件")

    def start_process_monitor(self, process):
        """启动进程监控线程，当股票软件关闭时自动关闭登录程序"""
        def monitor_process():
            try:
                self.log_status("开始监控股票软件进程状态...")

                # 等待子进程结束
                process.wait()

                self.log_status("检测到股票软件已关闭，正在清理登录程序...")

                # 股票软件已关闭，清理登录程序
                self.cleanup_and_exit()

            except Exception as e:
                self.log_status(f"进程监控出错: {str(e)}")
                # 出错时也尝试清理
                self.cleanup_and_exit()

        # 在后台线程中监控
        monitor_thread = threading.Thread(target=monitor_process, daemon=True)
        monitor_thread.start()

    def cleanup_and_exit(self):
        """清理所有资源并退出程序"""
        try:
            self.log_status("正在清理所有资源...")

            # 删除进程标识文件
            try:
                import os
                pid_file = os.path.join(os.path.dirname(__file__), "login_process.pid")
                if os.path.exists(pid_file):
                    os.remove(pid_file)
                    self.log_status("进程标识文件已删除")
            except Exception as e:
                self.log_status(f"删除进程标识文件失败: {str(e)}")

            # 停止所有定时器
            self.is_syncing = False
            if hasattr(self, 'captcha_sync_timer') and self.captcha_sync_timer:
                try:
                    self.root.after_cancel(self.captcha_sync_timer)
                except:
                    pass

            # 关闭浏览器
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                except:
                    pass

            self.log_status("资源清理完成，程序即将退出")

            # 强制退出程序
            def force_exit():
                try:
                    import sys
                    import os
                    # 强制终止当前进程
                    os._exit(0)
                except:
                    pass

            # 延迟一点时间让日志输出完成
            self.root.after(500, force_exit)

        except Exception as e:
            # 如果清理失败，直接强制退出
            try:
                import sys
                import os
                os._exit(0)
            except:
                pass

    def hide_to_background(self):
        """将登录界面隐藏到后台运行，而不是完全关闭"""
        try:
            self.log_status("登录界面隐藏到后台，股票看图软件继续运行")

            # 隐藏窗口到后台
            self.root.withdraw()  # 隐藏窗口

            # 可选：最小化到系统托盘（如果需要的话）
            # self.root.iconify()  # 最小化窗口

            # 保持程序运行但界面隐藏
            # 这样股票看图软件就不会因为父进程关闭而关闭

            print("登录界面已隐藏到后台，股票看图软件继续运行")
            print("如需重新显示登录界面，请重新运行此程序")

        except Exception as e:
            self.log_status(f"隐藏界面时出错: {str(e)}")
            # 如果隐藏失败，就保持界面显示
            pass

    def show_from_background(self):
        """从后台恢复显示登录界面"""
        try:
            self.root.deiconify()  # 恢复显示窗口
            self.root.lift()       # 将窗口提到前台
            self.root.focus_force()  # 强制获取焦点
            self.log_status("登录界面已恢复显示")
        except Exception as e:
            self.log_status(f"恢复显示界面时出错: {str(e)}")
            pass

    def graceful_close(self):
        """优雅地关闭程序，不影响其他程序"""
        try:
            # 停止所有定时器
            self.is_syncing = False
            if hasattr(self, 'captcha_sync_timer') and self.captcha_sync_timer:
                try:
                    self.root.after_cancel(self.captcha_sync_timer)
                except:
                    pass
                self.captcha_sync_timer = None
            if hasattr(self, 'captcha_validation_timer') and self.captcha_validation_timer:
                try:
                    self.root.after_cancel(self.captcha_validation_timer)
                except:
                    pass
                self.captcha_validation_timer = None

            # 关闭浏览器
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver_initialized = False
                    self.driver = None
                except:
                    pass

            # 延迟关闭窗口，确保所有清理工作完成
            def close_window():
                try:
                    # 只销毁当前窗口，不调用quit()避免影响其他程序
                    if hasattr(self, 'root') and self.root:
                        self.root.destroy()
                except:
                    pass

            # 延迟关闭，确保股票软件完全启动
            self.root.after(200, close_window)

        except Exception as e:
            # 如果优雅关闭失败，强制关闭当前窗口
            try:
                if hasattr(self, 'root') and self.root:
                    self.root.destroy()
            except:
                pass


def hide_console_window():
    """隐藏控制台窗口"""
    try:
        import ctypes
        import sys

        # 获取控制台窗口句柄
        kernel32 = ctypes.windll.kernel32
        user32 = ctypes.windll.user32

        # 获取当前控制台窗口
        console_window = kernel32.GetConsoleWindow()

        if console_window != 0:
            # SW_HIDE = 0 (隐藏窗口)
            # SW_MINIMIZE = 6 (最小化窗口)
            user32.ShowWindow(console_window, 0)  # 隐藏控制台窗口
            print("控制台窗口已隐藏")

    except Exception as e:
        print(f"隐藏控制台窗口失败: {str(e)}")

def main():
    """主程序入口"""
    try:
        print("=" * 50)
        print("量化股票软件Tus - 启动中")
        print("=" * 50)
        print("正在初始化程序...")

        # 创建应用实例（会自动初始化浏览器）
        app = TushareUI()

        print("程序初始化完成，启动界面...")
        print("=" * 50)

        # 隐藏控制台窗口
        hide_console_window()

        # 运行应用
        app.run()

    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
