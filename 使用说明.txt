===============================================
量化股票软件完整版 - 使用说明
===============================================

🎉 恭喜！您的量化股票软件已成功打包完成！

📁 生成文件位置：
   dist\量化股票软件.exe (约193MB)

✨ 主要功能特点：
   ✓ 保持获取数据每分钟的限制量 - API调用频率自动控制
   ✓ 多股票回测，板块选择功能完整 - 支持主板、创业板、科创板等
   ✓ 用户友好的错误处理 - 技术错误改为"欢迎使用"提示
   ✓ 单文件exe - 无需额外依赖，一键运行

🚀 启动方法：
   1. 双击 dist\量化股票软件.exe
   2. 首次运行会显示登录注册界面
   3. 完成登录后自动启动股票看图软件
   4. 登录界面会自动隐藏到后台

📋 功能说明：

【登录注册功能】
- 支持手机号注册和登录
- 自动获取验证码图片
- 登录成功后获取Tushare Token
- 自动启动股票分析软件

【股票分析功能】
- 实时股票数据获取
- K线图表显示
- 技术指标分析
- 自定义策略编写

【回测功能】
- 单股票回测
- 多股票回测
- 板块回测（主板、创业板、科创板等）
- 策略效果分析

【数据管理】
- 智能缓存系统
- API限流保护
- 数据压缩存储
- 批量数据更新

【网页交易】
- 浏览器自动化
- 交易信号生成
- 智能交易决策

⚙️ 系统要求：
- 操作系统：Windows 7/10/11
- 内存：至少4GB RAM
- 磁盘空间：至少1GB可用空间
- 网络：需要网络连接获取股票数据

🔧 配置文件：
程序运行后会在同目录下生成以下配置文件：
- tushare_token.txt - Tushare API令牌
- browser_config.json - 浏览器配置
- multi_stock_config.json - 多股票配置
- user_config/ - 用户配置目录
- market_data_cache/ - 数据缓存目录

📊 板块选择功能：
支持以下板块的股票加载：
- 全部股票 - A股市场所有股票
- 主板 - 传统大中型企业
- 中小板 - 中小型企业
- 创业板 - 创新型企业
- 科创板 - 科技创新企业
- 北交所 - 北京证券交易所
- 沪深300 - 大盘蓝筹股
- 中证1000 - 中小盘股票
- 上证50 - 超大盘股

🛡️ 安全特性：
- API调用频率限制（每分钟最多450次）
- 数据缓存减少重复请求
- 错误处理不暴露技术细节
- 用户数据本地存储

⚠️ 注意事项：
1. 首次启动可能较慢，需要解压内部文件
2. 确保网络连接正常
3. 需要有效的Tushare账号和Token
4. 建议关闭杀毒软件的实时保护（可能误报）

🔍 故障排除：
1. 如果启动失败，检查是否有杀毒软件拦截
2. 如果数据获取失败，检查网络连接
3. 如果登录失败，检查手机号和验证码
4. 如果功能异常，尝试重新启动程序

📞 技术支持：
如遇到问题，请检查：
- 控制台输出的错误信息
- 网络连接状态
- Tushare Token有效性
- 系统防火墙设置

🎯 使用建议：
1. 建议在稳定的网络环境下使用
2. 定期清理缓存数据释放空间
3. 合理设置回测参数避免过度消耗API
4. 保存重要的策略代码和配置

===============================================
感谢使用量化股票软件！祝您投资顺利！
===============================================
