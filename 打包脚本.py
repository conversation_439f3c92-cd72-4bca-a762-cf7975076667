#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化股票软件完整版打包脚本
支持Windows、Linux、macOS
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 50)
    print("量化股票软件完整版打包脚本")
    print("=" * 50)
    print()

def check_python():
    """检查Python环境"""
    print("正在检查Python环境...")
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("❌ 错误: 需要Python 3.7或更高版本")
            return False
        print(f"✓ Python {version.major}.{version.minor}.{version.micro}")
        return True
    except Exception as e:
        print(f"❌ Python检查失败: {e}")
        return False

def check_pyinstaller():
    """检查并安装PyInstaller"""
    print("正在检查PyInstaller...")
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False

def check_required_files():
    """检查必要文件"""
    print("正在检查必要文件...")
    required_files = [
        "登录注册.py",
        "股票看图软件_增强版.py",
        "市场数据管理.py",
        "量化股票软件完整版.spec"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✓ {file}")
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✓ 所有必要文件检查完成")
    return True

def clean_build_files():
    """清理旧的打包文件"""
    print("正在清理旧的打包文件...")
    
    dirs_to_clean = ["dist", "build", "__pycache__"]
    files_to_clean = ["量化股票软件.exe"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✓ 已清理文件: {file_name}")

def create_required_files():
    """创建必要的目录和文件"""
    print("正在创建必要的目录和文件...")
    
    # 创建默认配置文件
    if not os.path.exists("tushare_token.txt"):
        with open("tushare_token.txt", "w", encoding="utf-8") as f:
            f.write("")
        print("✓ 创建默认token文件")
    
    if not os.path.exists("browser_config.json"):
        with open("browser_config.json", "w", encoding="utf-8") as f:
            json.dump({}, f)
        print("✓ 创建默认浏览器配置")
    
    if not os.path.exists("multi_stock_config.json"):
        with open("multi_stock_config.json", "w", encoding="utf-8") as f:
            json.dump({}, f)
        print("✓ 创建默认多股票配置")
    
    # 创建必要目录
    dirs_to_create = ["user_config", "market_data_cache", "策略示例"]
    for dir_name in dirs_to_create:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✓ 创建目录: {dir_name}")

def run_pyinstaller():
    """运行PyInstaller打包"""
    print("正在开始打包...")
    print("这可能需要几分钟时间，请耐心等待...")
    print()
    
    try:
        # 运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "量化股票软件完整版.spec"]

        # 在Windows上使用gbk编码，其他系统使用utf-8
        encoding = 'gbk' if os.name == 'nt' else 'utf-8'

        result = subprocess.run(cmd, capture_output=True, text=True, encoding=encoding, errors='ignore')

        if result.returncode == 0:
            print("✓ 打包完成！")
            return True
        else:
            print("❌ 打包失败！")
            print("错误信息:")
            if result.stderr:
                print(result.stderr)
            if result.stdout:
                print("输出信息:")
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def check_output():
    """检查输出文件"""
    exe_name = "量化股票软件.exe" if os.name == 'nt' else "量化股票软件"
    exe_path = os.path.join("dist", exe_name)
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path)
        print()
        print("=" * 50)
        print("🎉 打包成功！")
        print("=" * 50)
        print()
        print(f"生成的文件位置: {exe_path}")
        print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
        print()
        print("使用说明:")
        print("1. 双击生成的exe文件启动程序")
        print("2. 首次运行会显示登录注册界面")
        print("3. 登录成功后会自动启动股票看图软件")
        print("4. 所有功能已集成在一个exe文件中")
        print()
        print("功能特点:")
        print("✓ 保持获取数据每分钟的限制量")
        print("✓ 多股票回测，板块选择功能完整")
        print("✓ 错误处理优化，用户友好提示")
        print("✓ 单文件exe，无需额外依赖")
        print()
        
        # 询问是否测试
        try:
            test_choice = input("是否立即测试运行？(y/n): ").strip().lower()
            if test_choice in ['y', 'yes']:
                print("正在启动测试...")
                if os.name == 'nt':  # Windows
                    os.startfile(exe_path)
                else:  # Linux/macOS
                    subprocess.Popen([exe_path])
                print("测试程序已启动，请检查功能是否正常")
        except KeyboardInterrupt:
            print("\n用户取消测试")
        
        return True
    else:
        print("❌ 打包失败：未找到生成的exe文件")
        print("请检查打包过程中的错误信息")
        return False

def main():
    """主函数"""
    print_header()
    
    # 检查环境
    if not check_python():
        return False
    
    if not check_pyinstaller():
        return False
    
    if not check_required_files():
        return False
    
    # 准备打包
    clean_build_files()
    create_required_files()
    
    # 执行打包
    if not run_pyinstaller():
        return False
    
    # 检查结果
    return check_output()

if __name__ == "__main__":
    try:
        success = main()
        print()
        print("打包脚本执行完成")
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n脚本执行出错: {e}")
        sys.exit(1)
