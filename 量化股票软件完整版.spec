# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    # 核心Python模块
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('浏览器驱动管理.py', '.'),
    
    # 策略示例文件夹
    ('策略示例', '策略示例'),
    
    # 配置文件
    ('tushare_token.txt', '.'),
    ('browser_config.json', '.'),
    ('multi_stock_config.json', '.'),
    
    # 用户配置目录
    ('user_config', 'user_config'),
    
    # 缓存目录（如果存在）
    ('market_data_cache', 'market_data_cache'),
    
    # 驱动目录（如果存在）
    ('drivers', 'drivers'),
]

# 隐藏导入的模块
hiddenimports = [
    # 基础GUI库
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'tkinter.filedialog',
    
    # 数据处理库
    'tushare',
    'pandas',
    'numpy',
    'openpyxl',
    'xlrd',
    
    # 图表库
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.figure',
    'matplotlib.dates',
    'matplotlib.ticker',
    
    # 网络和浏览器库
    'selenium',
    'selenium.webdriver',
    'selenium.webdriver.edge.service',
    'selenium.webdriver.edge.options',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support',
    'selenium.webdriver.support.expected_conditions',
    'webdriver_manager',
    'webdriver_manager.microsoft',
    'webdriver_manager.chrome',
    'requests',
    'urllib3',
    
    # 图像处理库
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    
    # 系统和工具库
    'datetime',
    'threading',
    'json',
    'time',
    'os',
    'sys',
    'subprocess',
    'platform',
    'signal',
    'importlib',
    'importlib.util',
    'collections',
    'collections.deque',
    'gzip',
    'pickle',
    'base64',
    'io',
    'logging',
    'warnings',
    
    # 数据分析库
    'lxml',
    'bs4',
    'beautifulsoup4',
    
    # 内部模块
    '股票看图软件_增强版',
    '回测系统',
    '回测分析',
    '策略模板',
    '多股票回测系统',
    '技术指标库',
    '市场数据管理',
    '使用者监控',
    '浏览器驱动管理',
]

a = Analysis(
    ['登录注册.py'],  # 主入口文件
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'matplotlib.tests',
        'pandas.tests',
        'numpy.tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='量化股票软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
